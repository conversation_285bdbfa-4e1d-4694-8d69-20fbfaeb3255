import type { Metadata } from 'next'
import { D<PERSON>_<PERSON> } from 'next/font/google'
import './globals.css'
import Header from '@/components/Header'
import Footer from '@/components/Footer'
import ContactModal from '@/components/ContactModal'
import { Analytics } from '@vercel/analytics/next'
import { SpeedInsights } from '@vercel/speed-insights/next'

const dmSans = DM_Sans({
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
})

export const metadata: Metadata = {
  metadataBase: new URL('http://localhost:3000'),
  title: '<PERSON> - Personal Blog & Portfolio',
  description: 'Personal blog and portfolio of <PERSON> - Web Developer & AI Automation Specialist',
  keywords: '<PERSON>, web development, AI automation, blog, portfolio',
  authors: [{ name: '<PERSON>' }],
  icons: {
    icon: '/favicon.webp',
    shortcut: '/favicon.webp',
    apple: '/favicon.webp',
  },
  openGraph: {
    title: '<PERSON> - Personal Blog & Portfolio',
    description: 'Personal blog and portfolio of <PERSON> - Web Developer & AI Automation Specialist',
    url: 'https://ernestromelo.com',
    siteName: 'Ernst <PERSON>lo',
    type: 'website',
  },
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      </head>
      <body className={dmSans.className}>
        <div className="min-h-screen relative flex flex-col bg-gradient-to-br from-blue-50/80 via-white to-orange-50/60">
          {/* Subtle Background Pattern */}
          <div className="fixed inset-0 bg-grid-pattern-light opacity-30 pointer-events-none z-0"></div>
          <div className="fixed inset-0 bg-gradient-to-r from-transparent via-blue-50/20 to-transparent pointer-events-none z-0"></div>

          <Header />
          <main className="pt-20 relative z-10 flex-grow">
            {children}
          </main>
          <Footer />
          <ContactModal />
        </div>
        {/* Analytics - wrapped in try/catch to prevent errors */}
        {process.env.NODE_ENV === 'production' && (
          <>
            <Analytics />
            <SpeedInsights />
          </>
        )}
      </body>
    </html>
  )
}
