import { getBlogPosts } from '@/lib/markdown'
import BlogGridWithFilter from '@/components/BlogGridWithFilter'
import SubtleGradientBackground from '@/components/SubtleGradientBackground'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Blog | Ernst <PERSON> - Web Development & AI Automation',
  description: 'Explore insights on web development, AI automation, and technology. Learn about modern web technologies, business automation, and digital transformation.',
  openGraph: {
    title: 'Blog | <PERSON>',
    description: 'Explore insights on web development, AI automation, and technology.',
    type: 'website',
    url: 'https://ernestomelo.com/blog',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Blog | <PERSON>',
    description: 'Explore insights on web development, AI automation, and technology.',
  }
}

export default async function BlogPage() {
  const posts = await getBlogPosts()

  // Structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": "<PERSON>",
    "description": "Insights on web development, AI automation, and technology",
    "url": "https://ernestomelo.com/blog",
    "author": {
      "@type": "Person",
      "name": "<PERSON>lo",
      "url": "https://ernestomelo.com"
    },
    "blogPost": posts.map(post => ({
      "@type": "BlogPosting",
      "headline": post.title,
      "description": post.excerpt,
      "url": `https://ernestomelo.com/blog/${post.slug}`,
      "datePublished": post.date,
      "author": {
        "@type": "Person",
        "name": post.author || "Ernst Romelo"
      },
      "image": post.featuredImage
    }))
  }

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
      <SubtleGradientBackground />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 animate-fade-in">
            Blog
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto animate-slide-up">
            Explore my thoughts on web development, AI automation, and technology.
          </p>
        </div>

        {/* Blog Grid with Filter */}
        <div className="mb-20">
          <BlogGridWithFilter posts={posts} />
        </div>
      </div>
    </>
  )
}
