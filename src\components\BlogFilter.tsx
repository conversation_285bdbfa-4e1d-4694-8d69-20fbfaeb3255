'use client'

import { useState, useEffect } from 'react'
import { BlogPost } from '@/types'

interface BlogFilterProps {
  posts: BlogPost[]
  onFilterChange: (filteredPosts: BlogPost[]) => void
}

export default function BlogFilter({ posts, onFilterChange }: BlogFilterProps) {
  const [activeFilter, setActiveFilter] = useState<string>('All')
  
  // Extract unique categories from posts
  const allCategories = Array.from(
    new Set(
      posts.flatMap(post => post.categories || [])
    )
  ).sort()

  // Use only categories for filter options
  const filterOptions = [
    'All',
    ...allCategories
  ]

  // Get post count for each filter
  const getPostCount = (filter: string) => {
    if (filter === 'All') return posts.length
    return posts.filter(post =>
      post.categories && post.categories.includes(filter)
    ).length
  }

  // Filter posts based on active filter
  useEffect(() => {
    if (activeFilter === 'All') {
      onFilterChange(posts)
    } else {
      const filtered = posts.filter(post =>
        post.categories && post.categories.includes(activeFilter)
      )
      onFilterChange(filtered)
    }
  }, [activeFilter, posts, onFilterChange])

  const handleFilterClick = (filter: string) => {
    setActiveFilter(filter)
  }

  return (
    <div className="mb-8">
      <div className="flex items-center flex-wrap">
        <span className="text-gray-700 font-medium mr-2">Filter by</span>
        {filterOptions.map((filter, index) => {
          const count = getPostCount(filter)
          const isActive = activeFilter === filter

          return (
            <span key={filter} className="inline-flex items-center">
              <button
                onClick={() => handleFilterClick(filter)}
                className={`
                  px-0 py-1 text-sm font-bold transition-colors duration-200 border-none bg-transparent
                  ${isActive
                    ? 'text-red-500'
                    : 'text-gray-500 hover:text-gray-700'
                  }
                `}
              >
                {filter}
                <sup className={`ml-1 text-xs ${isActive ? 'text-red-400' : 'text-gray-400'}`}>
                  {String(count).padStart(2, '0')}
                </sup>
              </button>
              {index < filterOptions.length - 1 && (
                <span className="mx-2 text-gray-400">/</span>
              )}
            </span>
          )
        })}
      </div>
    </div>
  )
}
