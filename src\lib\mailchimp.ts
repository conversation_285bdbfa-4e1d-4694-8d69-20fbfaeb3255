// Using direct HTTP calls instead of the Mailchimp package to avoid caching issues

export interface NewsletterSubscription {
  email: string;
  firstName?: string;
  lastName?: string;
}

export interface MailchimpResponse {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * Subscribe an email to the Mailchimp audience
 */
export async function subscribeToNewsletter(
  subscription: NewsletterSubscription
): Promise<MailchimpResponse> {
  try {
    const audienceId = process.env.MAILCHIMP_AUDIENCE_ID;
    const apiKey = process.env.MAILCHIMP_API_KEY;

    // Debug logging
    console.log('=== MAILCHIMP DEBUG ===');
    console.log('MAILCHIMP_AUDIENCE_ID:', audienceId);
    console.log('MAILCHIMP_API_KEY:', apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT SET');
    console.log('=======================');

    if (!audienceId) {
      console.error('Mailchimp audience ID not configured');
      throw new Error('Mailchimp audience ID not configured');
    }

    if (!apiKey) {
      console.error('Mailchimp API key not configured');
      throw new Error('Mailchimp API key not configured');
    }

    // Extract server from API key
    const server = apiKey.split('-')[1] || 'us12';
    const baseUrl = `https://${server}.api.mailchimp.com/3.0`;

    console.log('Using server:', server);
    console.log('Base URL:', baseUrl);
    console.log('Audience ID:', audienceId);

    // Create Basic Auth header
    const auth = Buffer.from(`user:${apiKey}`).toString('base64');

    // Subscribe the email using direct HTTP call
    const response = await fetch(`${baseUrl}/lists/${audienceId}/members`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email_address: subscription.email,
        status: 'subscribed',
        merge_fields: {
          FNAME: subscription.firstName || '',
          LNAME: subscription.lastName || '',
        },
        tags: ['Website Newsletter'],
      }),
    });

    console.log('Response status:', response.status);

    const responseData = await response.json();
    console.log('Response data:', responseData);

    if (!response.ok) {
      // Handle specific Mailchimp errors
      if (response.status === 400 && responseData.title === 'Member Exists') {
        return {
          success: false,
          message: 'This email is already subscribed to our newsletter.',
        };
      }

      if (response.status === 400 && responseData.title === 'Invalid Resource') {
        return {
          success: false,
          message: 'Please enter a valid email address.',
        };
      }

      throw new Error(`Mailchimp API error: ${responseData.detail || responseData.title || 'Unknown error'}`);
    }

    return {
      success: true,
      message: 'Successfully subscribed to newsletter!',
      data: {
        id: responseData.id,
        email: responseData.email_address,
        status: responseData.status,
      },
    };
  } catch (error: any) {
    console.error('Mailchimp subscription error:', error);

    return {
      success: false,
      message: 'Failed to subscribe to newsletter. Please try again later.',
    };
  }
}

/**
 * Unsubscribe an email from the Mailchimp audience
 */
export async function unsubscribeFromNewsletter(email: string): Promise<MailchimpResponse> {
  try {
    const audienceId = process.env.MAILCHIMP_AUDIENCE_ID;
    
    if (!audienceId) {
      throw new Error('Mailchimp audience ID not configured');
    }

    await mailchimp.lists.updateListMember(audienceId, email, {
      status: 'unsubscribed',
    });

    return {
      success: true,
      message: 'Successfully unsubscribed from newsletter.',
    };
  } catch (error: any) {
    console.error('Mailchimp unsubscribe error:', error);
    
    if (error.status === 404) {
      return {
        success: false,
        message: 'Email not found in our newsletter list.',
      };
    }

    return {
      success: false,
      message: 'Failed to unsubscribe. Please try again later.',
    };
  }
}

/**
 * Get subscriber information
 */
export async function getSubscriberInfo(email: string): Promise<MailchimpResponse> {
  try {
    const audienceId = process.env.MAILCHIMP_AUDIENCE_ID;
    
    if (!audienceId) {
      throw new Error('Mailchimp audience ID not configured');
    }

    const member = await mailchimp.lists.getListMember(audienceId, email);

    return {
      success: true,
      message: 'Subscriber found.',
      data: {
        email: member.email_address,
        status: member.status,
        firstName: member.merge_fields.FNAME,
        lastName: member.merge_fields.LNAME,
        subscribeDate: member.timestamp_signup,
      },
    };
  } catch (error: any) {
    console.error('Mailchimp get subscriber error:', error);
    
    if (error.status === 404) {
      return {
        success: false,
        message: 'Subscriber not found.',
      };
    }

    return {
      success: false,
      message: 'Failed to get subscriber information.',
    };
  }
}
