'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { ProjectInquiryData } from '@/types'
import { sendProjectInquiryEmail, getUserLocation, formatCurrency } from '@/lib/email'

export default function ProjectInquiryForm() {
  const [formData, setFormData] = useState<ProjectInquiryData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    website: '',
    title: '',
    service: '',
    description: '',
    budget: '',
    deadline: '',
    hearAbout: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [detectedCurrency, setDetectedCurrency] = useState('USD')
  const [isLoadingCurrency, setIsLoadingCurrency] = useState(true)

  // Detect user's currency on component mount
  useEffect(() => {
    const detectCurrency = async () => {
      try {
        const location = await getUserLocation()
        setDetectedCurrency(location.currency)
      } catch (error) {
        console.error('Error detecting currency:', error)
        // Fallback to USD
        setDetectedCurrency('USD')
      } finally {
        setIsLoadingCurrency(false)
      }
    }

    detectCurrency()
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Format budget with currency code before sending
      const formattedBudget = formData.budget
        ? `${detectedCurrency} ${formData.budget}`
        : ''

      const dataToSend = {
        ...formData,
        budget: formattedBudget
      }

      const success = await sendProjectInquiryEmail(dataToSend)
      if (success) {
        setIsSubmitted(true)
      } else {
        alert('Failed to send inquiry. Please try again.')
      }
    } catch (error) {
      console.error('Error submitting form:', error)
      alert('Failed to send inquiry. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }



  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-green-50 border border-green-200 rounded-lg p-8 text-center"
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-green-800 mb-2">Thank you for your inquiry!</h3>
        <p className="text-green-700">
          I&apos;ll review your project details and get back to you within 24 hours.
        </p>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white p-8"
    >
      {/* Header Section */}
      <div className="mb-12">
        <p className="text-sm text-gray-600 mb-4">Get started below.</p>
        <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
          Get a personalized proposal sent straight to your inbox in minutes.
        </h2>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Row 1: Full Name, Email, Phone */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Full Name (required)
            </label>
            <div className="relative">
              <input
                type="text"
                id="name"
                name="name"
                required
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Your full name"
                className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
              Email Address (required)
            </label>
            <input
              type="email"
              id="email"
              name="email"
              required
              value={formData.email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder="+*********"
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
            />
          </div>
        </div>

        {/* Row 2: Company, Website, Service */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">
              Company / Organization
            </label>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleInputChange}
              placeholder="Company name (optional)"
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
            />
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium text-gray-700 mb-2">
              Website or Social Media
            </label>
            <input
              type="url"
              id="website"
              name="website"
              value={formData.website}
              onChange={handleInputChange}
              placeholder="https://yourbrand.com or social link"
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
            />
          </div>

          <div>
            <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
              Type of Service You Need
            </label>
            <select
              id="service"
              name="service"
              required
              value={formData.service}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 select-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all appearance-none"
              style={{ fontSize: formData.service === '' ? '0.8125rem' : '0.875rem' }}
            >
              <option value="">—Please choose an option—</option>
              <option value="web-development">Web Development</option>
              <option value="ai-automation">AI Automation</option>
              <option value="both">Both Services</option>
              <option value="consultation">Consultation</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>

        {/* Row 3: Project Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
            Brief Description of Your Project (required)
          </label>
          <textarea
            id="description"
            name="description"
            required
            rows={6}
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Tell us about your project, goals, timeline, etc."
            className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all resize-none"
          />
        </div>

        {/* Row 4: Budget, Deadline, How did you hear */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">
              Estimated Budget
            </label>
            <div className="relative">
              {!isLoadingCurrency && (
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400 z-10">
                  {detectedCurrency}
                </div>
              )}
              <input
                type="number"
                id="budget"
                name="budget"
                value={formData.budget}
                onChange={handleInputChange}
                placeholder={
                  isLoadingCurrency
                    ? "Loading currency..."
                    : "10000"
                }
                min="0"
                step="1"
                className={`w-full py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all ${
                  !isLoadingCurrency ? 'pl-12 pr-4' : 'px-4'
                }`}
              />
            </div>
          </div>

          <div>
            <label htmlFor="deadline" className="block text-sm font-medium text-gray-700 mb-2">
              Preferred Deadline
            </label>
            <input
              type="date"
              id="deadline"
              name="deadline"
              value={formData.deadline}
              onChange={handleInputChange}
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
              style={{ fontSize: '0.8125rem' }}
            />
          </div>

          <div>
            <label htmlFor="hearAbout" className="block text-sm font-medium text-gray-700 mb-2">
              How did you hear about us?
            </label>
            <input
              type="text"
              id="hearAbout"
              name="hearAbout"
              value={formData.hearAbout}
              onChange={handleInputChange}
              placeholder="Google, Social Media, Friend, etc."
              className="w-full px-4 py-3 bg-gray-50 border-0 rounded-lg text-gray-900 placeholder-gray-500 placeholder-xs focus:bg-white focus:ring-2 focus:ring-gray-200 transition-all"
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="pt-4">
          <motion.button
            type="submit"
            disabled={isSubmitting}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="px-8 py-3 bg-gray-900 text-white font-medium rounded-lg hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300"
          >
            {isSubmitting ? 'Sending...' : 'Request a Quote'}
          </motion.button>
        </div>
      </form>
    </motion.div>
  )
}
